import express from "express";
import cors from "cors";
import { createServer } from "http";
import { Server } from "socket.io";
import { createClient } from "redis";
import { pid, REDIS_HOST, REDIS_PORT } from "./config";
import { setupSwagger } from "./swagger";

const app = express();
// Khởi động Swagger
setupSwagger(app);
const port = process.env.PORT || 3000;
var server = createServer(app);
var io = new Server(server, {
  cors: { origin: "*" },
  allowEIO3: true,
});

app.use(express.json({ limit: "50mb" }));

app.use(
  cors({
    origin: "*",
  })
);

export const redis = createClient({
  socket: {
    host: REDIS_HOST,
    port: REDIS_PORT,
  },
  // password: "81951ebfb79a444882431419cc4c66a0",
});
redis.connect();
//
// load initModuleIndex function
import settingRouter from "./router/setting";
import winiRouter from "./router/wini";
import dataRouter from "./router/data";
import integrationRouter from "./router/intergration";
import uploadFileRoute from "./router/uploadFile";
import vnPayRouter from "./router/payment";
import googleAuthen from "./router/googleAuthen";
import { randomGID } from "./Ultis/convert";
import { error } from "console";
import { sendNotificationCall, sendNotificationMessage } from "./function/notification";
app.use("/api/wini", winiRouter);
app.use("/api/setting", settingRouter);
app.use("/api/data", dataRouter);
app.use("/api/intergration", integrationRouter);
app.use("/api/file", uploadFileRoute);
app.use("/api/vnpay", vnPayRouter);
app.use("/api/2fa", googleAuthen);

// process.env.PORT
server.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
  console.log(`Swagger docs available at http://localhost:${port}/api-docs`);
});

interface User {
  id: string;
  name: string;
  socketId: string;
}

interface ChatMessage {
  Id?: string;
  Content?: string;
  text?: string; // fallback field
  DateCreated?: number;
  Type?: string;
  FileUrl?: string;
  CustomerId?: string;
  ChatRoomId?: string;
  user?: {
    Id?: string;
    Name?: string;
    Avatar?: string;
  };
  received?: boolean;
}

let onlineUsers: User[] = [];
const userRooms = new Map<string, Set<string>>();
const userSockets = new Map<string, string>(); // CustomerId -> socketId mapping
const typingTimeouts = new Map<string, NodeJS.Timeout>(); // roomId:userId -> timeout
const rateLimitMap = new Map<string, { count: number; resetTime: number }>(); // socketId -> rate limit info

// Rate limiting configuration
const RATE_LIMIT = {
  MESSAGE: { maxRequests: 30, windowMs: 60000 }, // 30 messages per minute
  TYPING: { maxRequests: 10, windowMs: 10000 }   // 10 typing events per 10 seconds
};

// Input validation helpers
function validateRoomId(roomId: any): boolean {
  return typeof roomId === 'string' && roomId.trim().length > 0 && roomId.length <= 100;
}

function validateMessage(message: any): message is ChatMessage {
  if (!message || typeof message !== 'object') return false;

  // Check if message has content in any of the expected fields
  const hasContent = message.Content || message.text || (typeof message === 'string');
  if (!hasContent) return false;

  // Validate Content field if exists
  if (message.Content && typeof message.Content === 'string') {
    if (message.Content.trim().length === 0 || message.Content.length > 5000) return false;
  }

  // Validate text field if exists (fallback)
  if (message.text && typeof message.text === 'string') {
    if (message.text.trim().length === 0 || message.text.length > 5000) return false;
  }

  // Validate Type field if exists
  if (message.Type && typeof message.Type !== 'string') return false;

  // Validate FileUrl if exists
  if (message.FileUrl && typeof message.FileUrl !== 'string') return false;

  // Validate user object if exists
  if (message.user && typeof message.user !== 'object') return false;
  if (message.user?.Name && typeof message.user.Name !== 'string') return false;

  return true;
}



function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

function sanitizeMessageObject(message: any): ChatMessage | string {
  if (!message || typeof message !== 'object') {
    // Handle case where message is a plain string
    if (typeof message === 'string') {
      return sanitizeInput(message);
    }
    return message;
  }

  const sanitized = { ...message };

  // Sanitize Content field
  if (sanitized.Content && typeof sanitized.Content === 'string') {
    sanitized.Content = sanitizeInput(sanitized.Content);
  }

  // Sanitize text field (fallback)
  if (sanitized.text && typeof sanitized.text === 'string') {
    sanitized.text = sanitizeInput(sanitized.text);
  }

  // Sanitize FileUrl if it's a string (basic validation)
  if (sanitized.FileUrl && typeof sanitized.FileUrl === 'string') {
    sanitized.FileUrl = sanitized.FileUrl.trim();
  }

  // Sanitize user object fields
  if (sanitized.user && typeof sanitized.user === 'object') {
    if (sanitized.user.Name && typeof sanitized.user.Name === 'string') {
      sanitized.user.Name = sanitizeInput(sanitized.user.Name);
    }
    if (sanitized.user.Avatar && typeof sanitized.user.Avatar === 'string') {
      sanitized.user.Avatar = sanitized.user.Avatar.trim();
    }
  }

  // Ensure required fields have default values
  if (!sanitized.Id) {
    sanitized.Id = randomGID();
  }

  if (!sanitized.DateCreated) {
    sanitized.DateCreated = new Date().getTime();
  }

  if (!sanitized.Type) {
    sanitized.Type = '1'; // Default to text
  }

  return sanitized;
}

// Helper function to resolve CustomerId to current socket ID
function getSocketIdByCustomerId(customerId: string): string | null {
  return userSockets.get(customerId) || null;
}

// Helper function to emit to user by CustomerId (handles reconnection)
function emitToUser(customerId: string, event: string, data: any): boolean {
  const socketId = getSocketIdByCustomerId(customerId);
  if (socketId) {
    io.to(socketId).emit(event, data);
    return true;
  }
  return false;
}

function checkRateLimit(socketId: string, eventType: 'MESSAGE' | 'TYPING'): boolean {
  const now = Date.now();
  const limit = RATE_LIMIT[eventType];
  const key = `${socketId}:${eventType}`;

  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, { count: 1, resetTime: now + limit.windowMs });
    return true;
  }

  const rateLimitInfo = rateLimitMap.get(key)!;

  if (now > rateLimitInfo.resetTime) {
    rateLimitInfo.count = 1;
    rateLimitInfo.resetTime = now + limit.windowMs;
    return true;
  }

  if (rateLimitInfo.count >= limit.maxRequests) {
    return false;
  }

  rateLimitInfo.count++;
  return true;
}

// Cleanup expired rate limit entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, rateLimitInfo] of rateLimitMap.entries()) {
    if (now > rateLimitInfo.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}, 5 * 60 * 1000);

io.on("connection", function (socket) {
  console.log("ID ket noi server: " + socket.id);
  console.log("onlineUsers: " + onlineUsers.length);

  const { CustomerId, Name } = socket.handshake.auth;
  console.log("CustomerId: " + CustomerId);

  try {
    // Remove existing user if already online (handle reconnection)
    onlineUsers = onlineUsers.filter(user => user.id !== CustomerId);

    // Add user to online list
    onlineUsers.push({ id: CustomerId, name: Name, socketId: socket.id });
    userSockets.set(CustomerId, socket.id);

    socket.emit("onlineUsers", onlineUsers);
    socket.broadcast.emit("user-online", CustomerId);

    //#region Chat Application
    socket.on("join-rooms", (roomData) => {
      try {
        // Support both single roomId and array of roomIds
        let roomIds: string[];
        if (typeof roomData?.roomId === 'string') {
          roomIds = [roomData.roomId];
        } else if (Array.isArray(roomData.roomId)) {
          roomIds = roomData.roomId;
        } else {
          socket.emit("error", { message: "Invalid room data format", roomData });
          return;
        }

        // Validate room IDs
        const validRoomIds = roomIds.filter(roomId => validateRoomId(roomId));
        if (validRoomIds.length === 0) {
          socket.emit("error", { message: "No valid room IDs provided" });
          return;
        }

        if (!userRooms.has(CustomerId)) {
          userRooms.set(CustomerId, new Set());
        }

        validRoomIds.forEach((roomId: string) => {
          socket.join(roomId);
          userRooms.get(CustomerId)!.add(roomId);
          console.log(`User ${CustomerId} joined room ${roomId}, socket ${socket.id}`);
          io.to(roomId).emit("user-join-room", {
            CustomerId: CustomerId,
            name: sanitizeInput(Name)
          });
        });

        console.log(`User ${CustomerId} joined ${validRoomIds.length} room(s): ${validRoomIds.join(', ')}`);
      } catch (error) {
        console.error("Error in join-rooms:", error);
        socket.emit("error", { message: "Failed to join rooms" });
      }
    });
    //leave room
    socket.on("leave-rooms", (roomData) => {
      try {
        // Support both single roomId and array of roomIds
        let roomIds: string[];
        if (typeof roomData?.roomId === 'string') {
          roomIds = [roomData.roomId];
        } else if (Array.isArray(roomData.roomId)) {
          roomIds = roomData.roomId;
        } else {
          socket.emit("error", { message: "Invalid room data format" });
          return;
        }

        // Validate room IDs
        const validRoomIds = roomIds.filter(roomId => validateRoomId(roomId));
        if (validRoomIds.length === 0) {
          socket.emit("error", { message: "No valid room IDs provided" });
          return;
        }

        if (!userRooms.has(CustomerId)) {
          userRooms.set(CustomerId, new Set());
        }

        validRoomIds.forEach((roomId: string) => {
          socket.leave(roomId);
          userRooms.get(CustomerId)!.delete(roomId);
          console.log(`User ${CustomerId} left room ${roomId}, socket ${socket.id}`);
          io.to(roomId).emit("user-leave-room", {
            CustomerId: CustomerId,
            name: sanitizeInput(Name)
          });
        });

        console.log(`User ${CustomerId} left ${validRoomIds.length} room(s): ${validRoomIds.join(', ')}`);
      } catch (error) {
        console.error("Error in leave-rooms:", error);
        socket.emit("error", { message: "Failed to leave rooms" });
      }
    });
    socket.on('message-read', async (data) => {
      try {
        const { roomId, messageId, readAt } = data;
        // Update database - mark message as read    
        // Broadcast read receipt to other users in the room
        socket.to(roomId).emit('message-read', {
          roomId,
          messageId,
          readAt
        });
        console.log(`Read receipt sent for message ${messageId}`);
      } catch (error) {
        console.error('Error handling message read:', error);
      }
    });

    // Gửi tin nhắn
    socket.on("send-message", ({ roomId, message, targetCustomerId }) => {
      try {
        // Rate limiting check
        if (!checkRateLimit(socket.id, 'MESSAGE')) {
          socket.emit("error", { message: "Rate limit exceeded for messages" });
          return;
        }

        // Validate input
        if (!validateRoomId(roomId)) {
          socket.emit("error", { message: "Invalid room ID" });
          return;
        }
        if (targetCustomerId) {
          //kiểm tra targetCustomerId có trong room không          
          targetCustomerId.forEach((customerId: string) => {
            //check user online 
            console.log("userSockets-Online", userSockets.has(customerId));
            if (!userSockets.has(customerId)) {
              //push noti
              sendNotificationMessage({ pid, data: { fromName: Name, roomId: roomId, customerId: customerId } });
            } else {
              //emit to user
              const userInRoom = userRooms.has(customerId) && userRooms.get(customerId)!.has(roomId);
              if (!userInRoom) {
                console.log("userRooms-Online: ", userInRoom);
                //gửi tin nhắn trực tiếp nếu không có trong room
                emitToUser(customerId, "receive-message", {
                  roomId,
                  fromUserId: CustomerId,
                  message,
                });
              } else {
                //gửi tin nhắn trong room
                console.log("userRooms-Online: ", userInRoom);
                io.to(roomId).emit("receive-message", {
                  roomId,
                  fromUserId: CustomerId,
                  message,
                });
              }
            }

          });
        }

        // const userInRoom = userRooms.has(CustomerId) && userRooms.get(CustomerId)!.has(roomId);
        // console.log(`User ${CustomerId} room check: hasUser=${userRooms.has(CustomerId)}, inRoom=${userInRoom}, userRooms=`, userRooms.get(CustomerId));

        // if (!userInRoom) {
        //   socket.emit("error", { message: "You are not in this room" });
        //   return;
        // }

        // const sanitizedMessage = sanitizeMessageObject(message);
        // console.log(`send-message from ${CustomerId} to room ${roomId}:`, sanitizedMessage);

        // const messageData = {
        //   roomId,
        //   fromUserId: CustomerId,
        //   message: sanitizedMessage,
        //   timestamp: Date.now()
        // };

        // console.log(`Emitting receive-message to room ${roomId}:`, messageData);
        // io.to(roomId).emit("receive-message", messageData);
        // console.log(`Message emitted to room ${roomId} successfully`);
      } catch (error) {
        console.error("Error in send-message:", error);
        socket.emit("error", { message: "Failed to send message" });
      }
    });

    // Typing indicator
    socket.on("typing", ({ roomId }) => {
      try {
        console.log(`typing from ${CustomerId} in room: ${roomId}`);

        // Clear existing typing timeout
        const typingKey = `${roomId}:${CustomerId}`;
        if (typingTimeouts.has(typingKey)) {
          clearTimeout(typingTimeouts.get(typingKey)!);
        }

        // Emit typing event
        socket.to(roomId).emit("typing", {
          roomId,
          fromUserId: CustomerId
        });

        // Set timeout to emit stop-typing after 3 seconds
        const timeout = setTimeout(() => {
          socket.to(roomId).emit("stop-typing", {
            roomId,
            fromUserId: CustomerId
          });
          typingTimeouts.delete(typingKey);
        }, 3000);

        typingTimeouts.set(typingKey, timeout);
      } catch (error) {
        console.error("Error in typing:", error);
      }
    });

    // Stop typing explicitly
    socket.on("stop-typing", ({ roomId }) => {
      try {
        if (!validateRoomId(roomId)) return;

        const typingKey = `${roomId}:${CustomerId}`;
        if (typingTimeouts.has(typingKey)) {
          clearTimeout(typingTimeouts.get(typingKey)!);
          typingTimeouts.delete(typingKey);
        }

        socket.to(roomId).emit("stop-typing", {
          roomId,
          fromUserId: CustomerId
        });
      } catch (error) {
        console.error("Error in stop-typing:", error);
      }
    });
    //#endregion




    //#region Call Application
    const activeCalls = new Map(); // callHistoryId -> { caller, receiver, status, startTime, acceptTime }
    const callTimeouts = new Map(); // callHistoryId -> timeoutId
    function cleanupCall(callHistoryId: string) {
      clearCallTimeout(callHistoryId);
      activeCalls.delete(callHistoryId);
      console.log(`🧹 Cleaned up call ${callHistoryId}`);
    }
    function clearCallTimeout(callHistoryId: string) {
      if (callTimeouts.has(callHistoryId)) {
        clearTimeout(callTimeouts.get(callHistoryId));
        callTimeouts.delete(callHistoryId);
        console.log(`⏰ Cleared timeout for call ${callHistoryId}`);
      }
    }
    socket.on("candidate", (data) => {
      try {
        const { candidate, targetUserId, from, callHistoryId } = data;

        if (!targetUserId || !from) {
          socket.emit("error", { message: "Invalid candidate data - missing targetUserId or from" });
          return;
        }

        // Validate ICE candidate structure
        if (!candidate || (candidate.candidate === undefined && candidate.candidate !== "")) {
          socket.emit("error", { message: "Invalid ICE candidate structure" });
          return;
        }

        const success = emitToUser(targetUserId, "candidate", {
          candidate,
          from,
          callHistoryId
        });

        if (!success) {
          socket.emit("error", { message: "Target user not available" });
        }

      } catch (error) {
        console.error("❌ Error in candidate:", error);
        socket.emit("error", { message: "Internal server error in candidate" });
      }
    });
    socket.on("call-user", (data) => {
      try {
        const { targetUserId, from, fromName, fromAvatar, callHistoryId } = data;

        if (!targetUserId || !from || !callHistoryId) {
          socket.emit("error", { message: "Invalid call data - missing required fields" });
          return;
        }

        // Check if target user is online
        // if (!userSockets.has(targetUserId)) {
        //   console.log(`📱 User ${targetUserId} is offline, sending push notification`);
        //   // sendNotificationCall({ targetUserId, from, fromName, callHistoryId });
        //   socket.emit("user-offline", { targetUserId });
        //   return;
        // }

        // Check if user is already in a call
        for (const [callId, callData] of activeCalls.entries()) {
          if ((callData.caller === targetUserId || callData.receiver === targetUserId) &&
            callData.status !== 'ended') {
            socket.emit("error", { message: "User is already in a call" });
            return;
          }
        }

        console.log("📞 Initiating call:", data);

        // Track call state
        activeCalls.set(callHistoryId, {
          caller: from,
          receiver: targetUserId,
          status: 'ringing',
          startTime: Date.now()
        });

        // Set timeout 60s cho cuộc gọi
        const timeoutId = setTimeout(() => {
          console.log(`⏰ Call ${callHistoryId} timed out`);

          // Emit timeout event to both users
          emitToUser(from, "call-timeout", {
            targetUserId,
            callHistoryId,
            reason: 'no-answer'
          });
          emitToUser(targetUserId, "call-timeout", {
            from,
            callHistoryId,
            reason: 'no-answer'
          });

          // Cleanup call resources
          cleanupCall(callHistoryId);
        }, 60000); // 60 seconds

        callTimeouts.set(callHistoryId, timeoutId);

        // Send incoming call notification
        const success = emitToUser(targetUserId, "incoming-call", {
          from,
          socketId: socket.id,
          fromName,
          fromAvatar,
          callHistoryId
        });

        if (!success) {
          cleanupCall(callHistoryId);
          socket.emit("error", { message: "Failed to reach target user" });
        }

      } catch (error) {
        console.error("❌ Error in call-user:", error);
        socket.emit("error", { message: "Internal server error in call-user" });
      }
    });
    socket.on("accept-call", (data) => {
      try {
        const { targetUserId, from, callHistoryId } = data; // targetUserId = caller, from = receiver

        if (!targetUserId || !from || !callHistoryId) {
          socket.emit("error", { message: "Invalid accept-call data" });
          return;
        }

        // Clear timeout
        clearCallTimeout(callHistoryId);

        // Update call state
        if (activeCalls.has(callHistoryId)) {
          const callData = activeCalls.get(callHistoryId);
          callData.status = 'connected';
          callData.acceptTime = Date.now();
          console.log(`✅ Call ${callHistoryId} accepted by ${from}`);
        }

        // Notify caller that call was accepted
        const success = emitToUser(targetUserId, "accept-call", {
          from, // receiver ID
          acceptedBy: from, // receiver ID  
          callHistoryId
        });

        if (!success) {
          socket.emit("error", { message: "Failed to notify caller" });
          cleanupCall(callHistoryId);
        }

      } catch (error) {
        console.error("❌ Error in accept-call:", error);
        socket.emit("error", { message: "Internal server error in accept-call" });
      }
    });
    socket.on("reject-call", (data) => {
      try {
        const { from, callHistoryId } = data; // from = caller ID

        if (!from || !callHistoryId) {
          socket.emit("error", { message: "Invalid reject-call data" });
          return;
        }

        // Clear timeout
        clearCallTimeout(callHistoryId);

        // Update call state
        if (activeCalls.has(callHistoryId)) {
          activeCalls.get(callHistoryId).status = 'rejected';
          console.log(`❌ Call ${callHistoryId} rejected`);
        }

        // Notify caller that call was rejected
        const success = emitToUser(from, "reject-call", {
          from,
          callHistoryId,
          rejectedBy: data.rejectedBy || 'unknown'
        });

        // Cleanup call resources
        cleanupCall(callHistoryId);

        if (!success) {
          console.log(`⚠️ Failed to notify caller ${from} about rejection`);
        }

      } catch (error) {
        console.error("❌ Error in reject-call:", error);
        socket.emit("error", { message: "Internal server error in reject-call" });
      }
    });
    socket.on("offer", (data) => {
      try {
        const { to, offer, from, callHistoryId } = data;

        if (!to || !offer || !from) {
          socket.emit("error", { message: "Invalid offer data - missing required fields" });
          return;
        }

        // Validate SDP offer structure
        if (!offer.type || !offer.sdp) {
          socket.emit("error", { message: "Invalid SDP offer structure" });
          return;
        }

        if (offer.type !== 'offer') {
          socket.emit("error", { message: "Invalid offer type" });
          return;
        }

        console.log(`📤 Forwarding offer from ${from} to ${to}`);

        const success = emitToUser(to, "offer", {
          offer,
          from,
          callHistoryId
        });

        if (!success) {
          socket.emit("error", { message: "Failed to deliver offer to target user" });
        }

      } catch (error) {
        console.error("❌ Error in offer:", error);
        socket.emit("error", { message: "Internal server error in offer" });
      }
    });
    socket.on("answer", (data) => {
      try {
        const { to, answer, from, callHistoryId } = data;

        if (!to || !answer || !from) {
          socket.emit("error", { message: "Invalid answer data - missing required fields" });
          return;
        }

        // Validate SDP answer structure
        if (!answer.type || !answer.sdp) {
          socket.emit("error", { message: "Invalid SDP answer structure" });
          return;
        }

        if (answer.type !== 'answer') {
          socket.emit("error", { message: "Invalid answer type" });
          return;
        }

        console.log(`📤 Forwarding answer from ${from} to ${to}`);

        const success = emitToUser(to, "answer", {
          answer,
          from,
          callHistoryId
        });

        if (!success) {
          socket.emit("error", { message: "Failed to deliver answer to target user" });
        }

      } catch (error) {
        console.error("❌ Error in answer:", error);
        socket.emit("error", { message: "Internal server error in answer" });
      }
    });
    socket.on("end-call", (data) => {
    try {
      const { targetUserId, from, callHistoryId } = data;
      
      if (!targetUserId || !from) {
        socket.emit("error", { message: "Invalid end-call data" });
        return;
      }
      
      console.log(`📞 Ending call ${callHistoryId} from ${from} to ${targetUserId}`);
      
      // Update call state
      if (callHistoryId && activeCalls.has(callHistoryId)) {
        const callData = activeCalls.get(callHistoryId);
        callData.status = 'ended';
        callData.endTime = Date.now();
        
        // Calculate call duration if call was connected
        if (callData.acceptTime) {
          callData.duration = callData.endTime - callData.acceptTime;
          console.log(`📊 Call duration: ${callData.duration}ms`);
        }
      }
      
      // Notify other user about call end
      const success = emitToUser(targetUserId, "end-call", { 
        from, // người kết thúc cuộc gọi
        callHistoryId,
        endedBy: from
      });
      
      // Cleanup call resources
      if (callHistoryId) {
        cleanupCall(callHistoryId);
      }
      
      if (!success) {
        console.log(`⚠️ Failed to notify user ${targetUserId} about call end`);
      }
      
    } catch (error) {
      console.error("❌ Error in end-call:", error);
      socket.emit("error", { message: "Internal server error in end-call" });
    }
  });
  socket.on("get-call-status", (data) => {
    try {
      const { callHistoryId } = data;
      
      if (!callHistoryId) {
        socket.emit("error", { message: "Invalid call status query" });
        return;
      }
      
      const callData = activeCalls.get(callHistoryId);
      socket.emit("call-status", {
        callHistoryId,
        status: callData ? callData.status : 'not-found',
        data: callData || null
      });
      
    } catch (error) {
      console.error("❌ Error in get-call-status:", error);
      socket.emit("error", { message: "Internal server error in get-call-status" });
    }
  });
    //#endregion

    //#region Disconnect
    socket.on("disconnect", async function () {
      try {
        console.log("disconnect " + socket.id);

        // Clean up online users
        onlineUsers = onlineUsers.filter(user => user.id !== CustomerId);

        // Clean up user rooms mapping
        if (userRooms.has(CustomerId)) {
          const rooms = userRooms.get(CustomerId)!;
          rooms.forEach(roomId => {
            socket.to(roomId).emit("user-leave-room", {
              CustomerId: CustomerId,
              name: Name
            });
          });
          userRooms.delete(CustomerId);
        }

        // Clean up user socket mapping
        userSockets.delete(CustomerId);

        // Clean up typing timeouts for this user
        for (const [key, timeout] of typingTimeouts.entries()) {
          if (key.endsWith(`:${CustomerId}`)) {
            clearTimeout(timeout);
            typingTimeouts.delete(key);
          }
        }

        // Clean up rate limiting data
        for (const [key] of rateLimitMap.entries()) {
          if (key.startsWith(socket.id)) {
            rateLimitMap.delete(key);
          }
        }

        // Notify other users
        socket.broadcast.emit("user-offline", CustomerId);

        console.log(`User ${CustomerId} disconnected and cleaned up`);
      } catch (error) {
        console.error("Error in disconnect:", error);
      }
    });
    //#endregion

  } catch (error) {
    console.error("Error in socket connection:", error);
    socket.emit("error", { message: "Connection error" });
    socket.disconnect();
  }
});
